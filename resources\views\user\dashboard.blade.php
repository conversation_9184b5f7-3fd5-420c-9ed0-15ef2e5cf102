<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Dashboard - PawPortal</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #0f172a;
            color: #e2e8f0;
            min-height: 100vh;
            display: flex;
        }

        .sidebar {
            width: 260px;
            background-color: #1e293b;
            border-right: 1px solid #334155;
            padding: 25px 20px;
            display: flex;
            flex-direction: column;
        }

        .logo {
            display: flex;
            align-items: center;
            margin-bottom: 35px;
            padding-bottom: 20px;
            border-bottom: 1px solid #334155;
        }

        .logo i {
            font-size: 24px;
            color: #06b6d4;
            margin-right: 10px;
        }

        .logo h2 {
            font-size: 22px;
            font-weight: 700;
            color: #f1f5f9;
        }

        .nav-menu {
            list-style: none;
            flex-grow: 1;
        }

        .nav-menu li {
            margin-bottom: 5px;
        }

        .nav-menu a {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            text-decoration: none;
            color: #cbd5e1;
            font-weight: 500;
            border-radius: 8px;
            transition: all 0.2s ease;
        }

        .nav-menu a:hover {
            background-color: #334155;
            color: #06b6d4;
        }

        .nav-menu i {
            margin-right: 12px;
            font-size: 16px;
            width: 18px;
            text-align: center;
        }

        .logout-section {
            margin-top: auto;
            padding-top: 20px;
            border-top: 1px solid #334155;
        }

        .logout-btn {
            width: 100%;
            background-color: #dc2626;
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            font-size: 14px;
            transition: background-color 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .logout-btn:hover {
            background-color: #b91c1c;
        }

        .logout-btn i {
            margin-right: 8px;
        }

        .main-content {
            flex-grow: 1;
            padding: 30px 40px;
        }

        .header {
            margin-bottom: 30px;
        }

        .welcome-title {
            font-size: 28px;
            font-weight: 700;
            color: #f1f5f9;
            margin-bottom: 8px;
        }

        .welcome-subtitle {
            font-size: 16px;
            color: #94a3b8;
        }

        .quick-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background-color: #1e293b;
            border: 1px solid #334155;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: 700;
            color: #06b6d4;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #94a3b8;
            font-size: 14px;
        }

        .modules-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }

        .module-card {
            background-color: #1e293b;
            border: 1px solid #334155;
            border-radius: 12px;
            padding: 24px;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .module-card:hover {
            border-color: #06b6d4;
            transform: translateY(-2px);
        }

        .module-icon {
            font-size: 28px;
            color: #06b6d4;
            margin-bottom: 16px;
        }

        .module-title {
            font-size: 18px;
            font-weight: 600;
            color: #f1f5f9;
            margin-bottom: 8px;
        }

        .module-description {
            color: #94a3b8;
            font-size: 14px;
            line-height: 1.5;
        }

        @media (max-width: 768px) {
            body {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
            }
            
            .main-content {
                padding: 20px;
            }
            
            .welcome-title {
                font-size: 24px;
            }
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="logo">
            <i class="fas fa-paw"></i>
            <h2>PawPortal</h2>
        </div>
        
        <ul class="nav-menu">
            <li>
                <a href="{{ route('pet.health') }}">
                    <i class="fas fa-heart-pulse"></i>
                    Pet Health & Records
                </a>
            </li>
            <li>
                <a href="#">
                    <i class="fas fa-home"></i>
                    Pet Adoption & Shelter
                </a>
            </li>
            <li>
                <a href="#">
                    <i class="fas fa-stethoscope"></i>
                    Pet Connect & Consultation
                </a>
            </li>
            <li>
                <a href="#">
                    <i class="fas fa-search"></i>
                    Lost & Found Community
                </a>
            </li>
            <li>
                <a href="#">
                    <i class="fas fa-th-large"></i>
                    Multi-Pet Dashboard
                </a>
            </li>
        </ul>
        
        <div class="logout-section">
            <form method="POST" action="{{ route('logout') }}">
                @csrf
                <button type="submit" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    Logout
                </button>
            </form>
        </div>
    </div>

    <div class="main-content">
        <div class="header">
            <h1 class="welcome-title">Welcome back, {{ Auth::user()->name }}!</h1>
            <p class="welcome-subtitle">Select a module from the options below to get started.</p>
        </div>

        <div class="quick-stats">
            <div class="stat-card">
                <div class="stat-number">3</div>
                <div class="stat-label">Your Pets</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">2</div>
                <div class="stat-label">Upcoming Appointments</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">8</div>
                <div class="stat-label">Health Records</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">15</div>
                <div class="stat-label">Community Helps</div>
            </div>
        </div>

        <div class="modules-grid">
            <div class="module-card">
                <i class="fas fa-heart-pulse module-icon"></i>
                <h3 class="module-title">Pet Health & Records</h3>
                <p class="module-description">Manage vaccinations, medications, and medical history for all your pets.</p>
            </div>
            
            <div class="module-card">
                <i class="fas fa-home module-icon"></i>
                <h3 class="module-title">Pet Adoption & Shelter</h3>
                <p class="module-description">Browse available pets and connect with local shelters and rescues.</p>
            </div>
            
            <div class="module-card">
                <i class="fas fa-stethoscope module-icon"></i>
                <h3 class="module-title">Pet Connect & Consultation</h3>
                <p class="module-description">Schedule online consultations with certified veterinarians.</p>
            </div>
            
            <div class="module-card">
                <i class="fas fa-search module-icon"></i>
                <h3 class="module-title">Lost & Found Community</h3>
                <p class="module-description">Help reunite lost pets with their families in your community.</p>
            </div>
            
            <div class="module-card">
                <i class="fas fa-th-large module-icon"></i>
                <h3 class="module-title">Multi-Pet Dashboard</h3>
                <p class="module-description">Overview and manage multiple pets from a single dashboard.</p>
            </div>
        </div>
    </div>
</body>
</html>